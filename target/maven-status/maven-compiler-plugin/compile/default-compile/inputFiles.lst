/Users/<USER>/code/newproject/src/main/java/com/example/photoupload/exception/FileNotFoundException.java
/Users/<USER>/code/newproject/src/main/java/com/example/photoupload/config/FileUploadConfig.java
/Users/<USER>/code/newproject/src/main/java/com/example/photoupload/service/FileService.java
/Users/<USER>/code/newproject/src/main/java/com/example/photoupload/config/ReferrerCheckInterceptor.java
/Users/<USER>/code/newproject/src/main/java/com/example/photoupload/config/SecurityConfig.java
/Users/<USER>/code/newproject/src/main/java/com/example/photoupload/dto/FileUploadResponse.java
/Users/<USER>/code/newproject/src/main/java/com/example/photoupload/util/FileUtil.java
/Users/<USER>/code/newproject/src/main/java/com/example/photoupload/controller/FileController.java
/Users/<USER>/code/newproject/src/main/java/com/example/photoupload/model/FileInfo.java
/Users/<USER>/code/newproject/src/main/java/com/example/photoupload/config/WebConfig.java
/Users/<USER>/code/newproject/src/main/java/com/example/photoupload/repository/FileInfoRepository.java
/Users/<USER>/code/newproject/src/main/java/com/example/photoupload/exception/FileProcessingException.java
/Users/<USER>/code/newproject/src/main/java/com/example/photoupload/dto/ApiResponse.java
/Users/<USER>/code/newproject/src/main/java/com/example/photoupload/exception/GlobalExceptionHandler.java
/Users/<USER>/code/newproject/src/main/java/com/example/photoupload/PhotoUploadApplication.java
