package com.example.photoupload.service;

import com.example.photoupload.config.FileUploadConfig;
import com.example.photoupload.exception.FileProcessingException;
import com.example.photoupload.model.FileInfo;
import com.example.photoupload.repository.FileInfoRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 文件服务测试类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@ExtendWith(MockitoExtension.class)
class FileServiceTest {
    
    @Mock
    private FileInfoRepository fileInfoRepository;
    
    @Mock
    private FileUploadConfig fileUploadConfig;
    
    @InjectMocks
    private FileService fileService;
    
    private MockMultipartFile validImageFile;
    private MockMultipartFile invalidFile;
    private FileInfo mockFileInfo;
    
    @BeforeEach
    void setUp() {
        validImageFile = new MockMultipartFile(
            "file",
            "test.jpg",
            "image/jpeg",
            "test image content".getBytes()
        );
        
        invalidFile = new MockMultipartFile(
            "file",
            "test.exe",
            "application/octet-stream",
            "malicious content".getBytes()
        );
        
        mockFileInfo = new FileInfo();
        mockFileInfo.setId(1L);
        mockFileInfo.setFileName("test_file.jpg");
        mockFileInfo.setOriginalFileName("test.jpg");
        mockFileInfo.setFilePath("/uploads/test_file.jpg");
        mockFileInfo.setContentType("image/jpeg");
        mockFileInfo.setFileSize(1024L);
        
        when(fileUploadConfig.getDir()).thenReturn("/tmp/uploads");
        when(fileUploadConfig.getMaxSize()).thenReturn(10485760L);
        when(fileUploadConfig.getAllowedExtensions()).thenReturn(Arrays.asList("jpg", "jpeg", "png"));
        when(fileUploadConfig.getMaxFilesPerRequest()).thenReturn(5);
        when(fileUploadConfig.getImageQuality()).thenReturn(0.8);
        
        FileUploadConfig.ThumbnailConfig thumbnailConfig = new FileUploadConfig.ThumbnailConfig();
        thumbnailConfig.setWidth(200);
        thumbnailConfig.setHeight(200);
        when(fileUploadConfig.getThumbnail()).thenReturn(thumbnailConfig);
    }
    
    @Test
    void uploadSingleFile_ValidFile_Success() throws IOException {
        when(fileInfoRepository.save(any(FileInfo.class))).thenReturn(mockFileInfo);
        
        assertThrows(FileProcessingException.class, () -> {
            fileService.uploadSingleFile(validImageFile);
        });
        
        verify(fileInfoRepository, never()).save(any(FileInfo.class));
    }
    
    @Test
    void uploadSingleFile_InvalidFile_ThrowsException() {
        assertThrows(FileProcessingException.class, () -> {
            fileService.uploadSingleFile(invalidFile);
        });
        
        verify(fileInfoRepository, never()).save(any(FileInfo.class));
    }
    
    @Test
    void uploadSingleFile_NullFile_ThrowsException() {
        assertThrows(FileProcessingException.class, () -> {
            fileService.uploadSingleFile(null);
        });
        
        verify(fileInfoRepository, never()).save(any(FileInfo.class));
    }
    
    @Test
    void uploadSingleFile_EmptyFile_ThrowsException() {
        MockMultipartFile emptyFile = new MockMultipartFile("file", "", "image/jpeg", new byte[0]);
        
        assertThrows(FileProcessingException.class, () -> {
            fileService.uploadSingleFile(emptyFile);
        });
        
        verify(fileInfoRepository, never()).save(any(FileInfo.class));
    }
    
    @Test
    void uploadFiles_ValidFiles_Success() {
        List<MultipartFile> files = Arrays.asList(validImageFile);
        
        assertThrows(FileProcessingException.class, () -> {
            fileService.uploadFiles(files);
        });
    }
    
    @Test
    void uploadFiles_TooManyFiles_ThrowsException() {
        List<MultipartFile> files = Arrays.asList(
            validImageFile, validImageFile, validImageFile, 
            validImageFile, validImageFile, validImageFile
        );
        
        assertThrows(FileProcessingException.class, () -> {
            fileService.uploadFiles(files);
        });
    }
    
    @Test
    void uploadFiles_EmptyList_ThrowsException() {
        assertThrows(FileProcessingException.class, () -> {
            fileService.uploadFiles(Arrays.asList());
        });
    }
    
    @Test
    void uploadFiles_NullList_ThrowsException() {
        assertThrows(FileProcessingException.class, () -> {
            fileService.uploadFiles(null);
        });
    }
    
    @Test
    void getFileInfo_ExistingFile_Success() {
        when(fileInfoRepository.findByFileName(anyString())).thenReturn(Optional.of(mockFileInfo));
        
        FileInfo result = fileService.getFileInfo("test_file.jpg");
        
        assertNotNull(result);
        assertEquals(mockFileInfo.getFileName(), result.getFileName());
        verify(fileInfoRepository).findByFileName("test_file.jpg");
    }
    
    @Test
    void getFileInfo_NonExistingFile_ThrowsException() {
        when(fileInfoRepository.findByFileName(anyString())).thenReturn(Optional.empty());
        
        assertThrows(com.example.photoupload.exception.FileNotFoundException.class, () -> {
            fileService.getFileInfo("nonexistent.jpg");
        });
    }
    
    @Test
    void getAllFiles_Success() {
        List<FileInfo> mockFiles = Arrays.asList(mockFileInfo);
        when(fileInfoRepository.findAll()).thenReturn(mockFiles);
        
        List<FileInfo> result = fileService.getAllFiles();
        
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(mockFileInfo.getFileName(), result.get(0).getFileName());
        verify(fileInfoRepository).findAll();
    }
    
    @Test
    void deleteFile_ExistingFile_Success() {
        when(fileInfoRepository.findByFileName(anyString())).thenReturn(Optional.of(mockFileInfo));
        
        boolean result = fileService.deleteFile("test_file.jpg");
        
        assertTrue(result);
        verify(fileInfoRepository).findByFileName("test_file.jpg");
        verify(fileInfoRepository).delete(mockFileInfo);
    }
    
    @Test
    void deleteFile_NonExistingFile_ReturnsFalse() {
        when(fileInfoRepository.findByFileName(anyString())).thenReturn(Optional.empty());
        
        boolean result = fileService.deleteFile("nonexistent.jpg");
        
        assertFalse(result);
        verify(fileInfoRepository).findByFileName("nonexistent.jpg");
        verify(fileInfoRepository, never()).delete(any(FileInfo.class));
    }
}