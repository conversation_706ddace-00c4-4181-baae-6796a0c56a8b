package com.example.photoupload.controller;

import com.example.photoupload.dto.FileUploadResponse;
import com.example.photoupload.model.FileInfo;
import com.example.photoupload.service.FileService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 文件控制器测试类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@WebMvcTest(FileController.class)
class FileControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private FileService fileService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    private FileUploadResponse mockUploadResponse;
    private FileInfo mockFileInfo;
    private MockMultipartFile mockFile;
    
    @BeforeEach
    void setUp() {
        mockUploadResponse = new FileUploadResponse();
        mockUploadResponse.setId(1L);
        mockUploadResponse.setFileName("test_file.jpg");
        mockUploadResponse.setOriginalFileName("test.jpg");
        mockUploadResponse.setDownloadUrl("/api/files/download/test_file.jpg");
        mockUploadResponse.setPreviewUrl("/api/files/preview/test_file.jpg");
        mockUploadResponse.setContentType("image/jpeg");
        mockUploadResponse.setFileSize(1024L);
        mockUploadResponse.setUploadTime(LocalDateTime.now());
        
        mockFileInfo = new FileInfo();
        mockFileInfo.setId(1L);
        mockFileInfo.setFileName("test_file.jpg");
        mockFileInfo.setOriginalFileName("test.jpg");
        mockFileInfo.setFilePath("/uploads/test_file.jpg");
        mockFileInfo.setContentType("image/jpeg");
        mockFileInfo.setFileSize(1024L);
        
        mockFile = new MockMultipartFile(
            "file",
            "test.jpg",
            "image/jpeg",
            "test image content".getBytes()
        );
    }
    
    @Test
    @WithMockUser
    void uploadFile_ValidFile_Success() throws Exception {
        when(fileService.uploadSingleFile(any())).thenReturn(mockUploadResponse);
        
        mockMvc.perform(multipart("/files/upload")
                .file(mockFile)
                .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.fileName").value("test_file.jpg"))
                .andExpect(jsonPath("$.data.originalFileName").value("test.jpg"));
    }
    
    @Test
    @WithMockUser
    void uploadFiles_ValidFiles_Success() throws Exception {
        List<FileUploadResponse> responses = Arrays.asList(mockUploadResponse);
        when(fileService.uploadFiles(any())).thenReturn(responses);
        
        mockMvc.perform(multipart("/files/upload/batch")
                .file(mockFile)
                .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].fileName").value("test_file.jpg"));
    }
    
    @Test
    void downloadFile_ExistingFile_Success() throws Exception {
        when(fileService.getFileInfo(anyString())).thenReturn(mockFileInfo);
        when(fileService.getFileContent(anyString())).thenReturn("test content".getBytes());
        
        mockMvc.perform(get("/files/download/test_file.jpg"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("image/jpeg"))
                .andExpect(content().bytes("test content".getBytes()));
    }
    
    @Test
    void previewFile_ExistingFile_Success() throws Exception {
        when(fileService.getFileInfo(anyString())).thenReturn(mockFileInfo);
        when(fileService.getFileContent(anyString())).thenReturn("test content".getBytes());
        
        mockMvc.perform(get("/files/preview/test_file.jpg"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("image/jpeg"))
                .andExpect(header().string("Cache-Control", "max-age=3600"));
    }
    
    @Test
    @WithMockUser
    void listFiles_Success() throws Exception {
        List<FileInfo> files = Arrays.asList(mockFileInfo);
        when(fileService.getAllFiles()).thenReturn(files);
        
        mockMvc.perform(get("/files/list"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].fileName").value("test_file.jpg"));
    }
    
    @Test
    @WithMockUser
    void deleteFile_ExistingFile_Success() throws Exception {
        when(fileService.deleteFile(anyString())).thenReturn(true);
        
        mockMvc.perform(delete("/files/test_file.jpg")
                .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }
    
    @Test
    @WithMockUser
    void deleteFile_NonExistingFile_NotFound() throws Exception {
        when(fileService.deleteFile(anyString())).thenReturn(false);
        
        mockMvc.perform(delete("/files/nonexistent.jpg")
                .with(csrf()))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.success").value(false));
    }
    
    @Test
    @WithMockUser
    void getFileInfo_ExistingFile_Success() throws Exception {
        when(fileService.getFileInfo(anyString())).thenReturn(mockFileInfo);
        
        mockMvc.perform(get("/files/info/test_file.jpg"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.fileName").value("test_file.jpg"))
                .andExpect(jsonPath("$.data.originalFileName").value("test.jpg"));
    }
}