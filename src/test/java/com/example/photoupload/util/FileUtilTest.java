package com.example.photoupload.util;

import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockMultipartFile;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 文件工具类测试
 * 
 * <AUTHOR>
 * @version 1.0
 */
class FileUtilTest {
    
    @Test
    void isValidImageFile_ValidImageFile_ReturnsTrue() {
        MockMultipartFile file = new MockMultipartFile(
            "file", "test.jpg", "image/jpeg", "test content".getBytes()
        );
        
        assertTrue(FileUtil.isValidImageFile(file));
    }
    
    @Test
    void isValidImageFile_InvalidExtension_ReturnsFalse() {
        MockMultipartFile file = new MockMultipartFile(
            "file", "test.exe", "application/octet-stream", "test content".getBytes()
        );
        
        assertFalse(FileUtil.isValidImageFile(file));
    }
    
    @Test
    void isValidImageFile_InvalidContentType_ReturnsFalse() {
        MockMultipartFile file = new MockMultipartFile(
            "file", "test.jpg", "text/plain", "test content".getBytes()
        );
        
        assertFalse(FileUtil.isValidImageFile(file));
    }
    
    @Test
    void isValidImageFile_NullFile_ReturnsFalse() {
        assertFalse(FileUtil.isValidImageFile(null));
    }
    
    @Test
    void isValidImageFile_EmptyFile_ReturnsFalse() {
        MockMultipartFile file = new MockMultipartFile(
            "file", "test.jpg", "image/jpeg", new byte[0]
        );
        
        assertFalse(FileUtil.isValidImageFile(file));
    }
    
    @Test
    void getFileExtension_ValidFileName_ReturnsExtension() {
        assertEquals("jpg", FileUtil.getFileExtension("test.jpg"));
        assertEquals("png", FileUtil.getFileExtension("image.PNG"));
        assertEquals("jpeg", FileUtil.getFileExtension("photo.JPEG"));
    }
    
    @Test
    void getFileExtension_NoExtension_ReturnsNull() {
        assertNull(FileUtil.getFileExtension("filename"));
        assertNull(FileUtil.getFileExtension(""));
        assertNull(FileUtil.getFileExtension(null));
    }
    
    @Test
    void generateUniqueFileName_ValidInput_ReturnsUniqueFileName() {
        String fileName1 = FileUtil.generateUniqueFileName("test.jpg");
        String fileName2 = FileUtil.generateUniqueFileName("test.jpg");
        
        assertNotNull(fileName1);
        assertNotNull(fileName2);
        assertNotEquals(fileName1, fileName2);
        assertTrue(fileName1.endsWith(".jpg"));
        assertTrue(fileName2.endsWith(".jpg"));
    }
    
    @Test
    void sanitizeFileName_ValidInput_ReturnsSanitizedFileName() {
        assertEquals("test_file.jpg", FileUtil.sanitizeFileName("test file.jpg"));
        assertEquals("test_file.jpg", FileUtil.sanitizeFileName("test@#$%file.jpg"));
        assertEquals("test_file.jpg", FileUtil.sanitizeFileName("___test___file___.jpg"));
    }
    
    @Test
    void sanitizeFileName_NullInput_ReturnsNull() {
        assertNull(FileUtil.sanitizeFileName(null));
    }
    
    @Test
    void isValidFileSize_ValidSize_ReturnsTrue() {
        MockMultipartFile file = new MockMultipartFile(
            "file", "test.jpg", "image/jpeg", "test content".getBytes()
        );
        
        assertTrue(FileUtil.isValidFileSize(file, 1024L));
    }
    
    @Test
    void isValidFileSize_ExceedsLimit_ReturnsFalse() {
        MockMultipartFile file = new MockMultipartFile(
            "file", "test.jpg", "image/jpeg", "test content".getBytes()
        );
        
        assertFalse(FileUtil.isValidFileSize(file, 5L));
    }
    
    @Test
    void isValidFileSize_NullFile_ReturnsFalse() {
        assertFalse(FileUtil.isValidFileSize(null, 1024L));
    }
    
    @Test
    void formatFileSize_ValidSizes_ReturnsFormattedString() {
        assertEquals("512 B", FileUtil.formatFileSize(512L));
        assertEquals("1.0 KB", FileUtil.formatFileSize(1024L));
        assertEquals("1.0 MB", FileUtil.formatFileSize(1024L * 1024L));
        assertEquals("1.0 GB", FileUtil.formatFileSize(1024L * 1024L * 1024L));
    }
    
    @Test
    void isImageContentType_ValidImageType_ReturnsTrue() {
        assertTrue(FileUtil.isImageContentType("image/jpeg"));
        assertTrue(FileUtil.isImageContentType("image/png"));
        assertTrue(FileUtil.isImageContentType("image/gif"));
    }
    
    @Test
    void isImageContentType_InvalidType_ReturnsFalse() {
        assertFalse(FileUtil.isImageContentType("text/plain"));
        assertFalse(FileUtil.isImageContentType("application/pdf"));
        assertFalse(FileUtil.isImageContentType(null));
    }
    
    @Test
    void getContentTypeByExtension_ValidExtensions_ReturnsCorrectType() {
        assertEquals("image/jpeg", FileUtil.getContentTypeByExtension("jpg"));
        assertEquals("image/jpeg", FileUtil.getContentTypeByExtension("jpeg"));
        assertEquals("image/png", FileUtil.getContentTypeByExtension("png"));
        assertEquals("image/gif", FileUtil.getContentTypeByExtension("gif"));
        assertEquals("application/octet-stream", FileUtil.getContentTypeByExtension("unknown"));
        assertEquals("application/octet-stream", FileUtil.getContentTypeByExtension(null));
    }
}