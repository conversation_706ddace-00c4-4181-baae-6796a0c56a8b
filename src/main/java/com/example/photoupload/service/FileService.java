package com.example.photoupload.service;

import com.example.photoupload.config.FileUploadConfig;
import com.example.photoupload.dto.FileUploadResponse;
import com.example.photoupload.exception.FileProcessingException;
import com.example.photoupload.exception.FileNotFoundException;
import com.example.photoupload.model.FileInfo;
import com.example.photoupload.repository.FileInfoRepository;
import com.example.photoupload.util.FileUtil;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 文件服务类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional
public class FileService {
    
    private static final Logger logger = LoggerFactory.getLogger(FileService.class);
    
    @Autowired
    private FileInfoRepository fileInfoRepository;
    
    @Autowired
    private FileUploadConfig fileUploadConfig;
    
    public List<FileUploadResponse> uploadFiles(List<MultipartFile> files) {
        if (files == null || files.isEmpty()) {
            throw new FileProcessingException("没有选择要上传的文件");
        }
        
        if (files.size() > fileUploadConfig.getMaxFilesPerRequest()) {
            throw new FileProcessingException("一次最多只能上传 " + fileUploadConfig.getMaxFilesPerRequest() + " 个文件");
        }
        
        List<FileUploadResponse> responses = new ArrayList<>();
        
        for (MultipartFile file : files) {
            try {
                FileUploadResponse response = uploadSingleFile(file);
                responses.add(response);
            } catch (Exception e) {
                logger.error("上传文件失败: {}", file.getOriginalFilename(), e);
                throw new FileProcessingException("上传文件失败: " + file.getOriginalFilename());
            }
        }
        
        return responses;
    }
    
    public FileUploadResponse uploadSingleFile(MultipartFile file) {
        validateFile(file);
        
        try {
            String datePath = FileUtil.createDateBasedPath(fileUploadConfig.getDir());
            FileUtil.ensureDirectoryExists(datePath);
            
            String uniqueFileName = FileUtil.generateUniqueFileName(file.getOriginalFilename());
            String filePath = Paths.get(datePath, uniqueFileName).toString();
            
            file.transferTo(new File(filePath));
            
            FileInfo fileInfo = new FileInfo(
                uniqueFileName,
                file.getOriginalFilename(),
                filePath,
                file.getContentType(),
                file.getSize()
            );
            
            String thumbnailPath = createThumbnail(filePath, uniqueFileName, datePath);
            fileInfo.setThumbnailPath(thumbnailPath);
            
            fileInfo = fileInfoRepository.save(fileInfo);
            
            return buildFileUploadResponse(fileInfo);
            
        } catch (IOException e) {
            logger.error("文件保存失败", e);
            throw new FileProcessingException("文件保存失败: " + e.getMessage());
        }
    }
    
    @Cacheable(value = "fileContent", key = "#fileName")
    public byte[] getFileContent(String fileName) {
        Optional<FileInfo> fileInfoOpt = fileInfoRepository.findByFileName(fileName);
        if (fileInfoOpt.isEmpty()) {
            throw new FileNotFoundException("文件不存在: " + fileName);
        }
        
        FileInfo fileInfo = fileInfoOpt.get();
        fileInfo.incrementAccessCount();
        fileInfoRepository.save(fileInfo);
        
        try {
            Path filePath = Paths.get(fileInfo.getFilePath());
            return Files.readAllBytes(filePath);
        } catch (IOException e) {
            logger.error("读取文件失败: {}", fileName, e);
            throw new FileProcessingException("读取文件失败: " + e.getMessage());
        }
    }
    
    public FileInfo getFileInfo(String fileName) {
        return fileInfoRepository.findByFileName(fileName)
                .orElseThrow(() -> new FileNotFoundException("文件不存在: " + fileName));
    }
    
    public List<FileInfo> getAllFiles() {
        return fileInfoRepository.findAll();
    }
    
    public boolean deleteFile(String fileName) {
        Optional<FileInfo> fileInfoOpt = fileInfoRepository.findByFileName(fileName);
        if (fileInfoOpt.isEmpty()) {
            return false;
        }
        
        FileInfo fileInfo = fileInfoOpt.get();
        
        FileUtil.deleteFileIfExists(fileInfo.getFilePath());
        if (fileInfo.getThumbnailPath() != null) {
            FileUtil.deleteFileIfExists(fileInfo.getThumbnailPath());
        }
        
        fileInfoRepository.delete(fileInfo);
        return true;
    }
    
    public void cleanupOldFiles(int daysOld) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(daysOld);
        List<FileInfo> unusedFiles = fileInfoRepository.findUnusedFiles(cutoffTime);
        
        for (FileInfo fileInfo : unusedFiles) {
            deleteFile(fileInfo.getFileName());
        }
        
        logger.info("清理了 {} 个过期文件", unusedFiles.size());
    }
    
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new FileProcessingException("文件不能为空");
        }
        
        if (!FileUtil.isValidImageFile(file)) {
            throw new FileProcessingException("文件类型不支持，仅支持图片格式");
        }
        
        if (!FileUtil.isValidFileSize(file, fileUploadConfig.getMaxSize())) {
            throw new FileProcessingException("文件大小超出限制，最大支持 " + 
                FileUtil.formatFileSize(fileUploadConfig.getMaxSize()));
        }
    }
    
    private String createThumbnail(String originalFilePath, String fileName, String targetDir) {
        try {
            String thumbnailFileName = "thumb_" + fileName;
            String thumbnailPath = Paths.get(targetDir, thumbnailFileName).toString();
            
            Thumbnails.of(originalFilePath)
                    .size(fileUploadConfig.getThumbnail().getWidth(), 
                          fileUploadConfig.getThumbnail().getHeight())
                    .outputQuality(fileUploadConfig.getImageQuality())
                    .toFile(thumbnailPath);
            
            return thumbnailPath;
        } catch (IOException e) {
            logger.warn("创建缩略图失败: {}", originalFilePath, e);
            return null;
        }
    }
    
    private FileUploadResponse buildFileUploadResponse(FileInfo fileInfo) {
        String baseUrl = "/api/files";
        
        return new FileUploadResponse(
            fileInfo.getId(),
            fileInfo.getFileName(),
            fileInfo.getOriginalFileName(),
            baseUrl + "/download/" + fileInfo.getFileName(),
            baseUrl + "/preview/" + fileInfo.getFileName(),
            fileInfo.getThumbnailPath() != null ? baseUrl + "/thumbnail/" + fileInfo.getFileName() : null,
            fileInfo.getContentType(),
            fileInfo.getFileSize(),
            fileInfo.getUploadTime()
        );
    }
}