package com.example.photoupload.repository;

import com.example.photoupload.model.FileInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 文件信息数据访问层
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public interface FileInfoRepository extends JpaRepository<FileInfo, Long> {
    
    Optional<FileInfo> findByFileName(String fileName);
    
    List<FileInfo> findByContentTypeStartingWith(String contentType);
    
    @Query("SELECT f FROM FileInfo f WHERE f.uploadTime BETWEEN :startTime AND :endTime")
    List<FileInfo> findByUploadTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                          @Param("endTime") LocalDateTime endTime);
    
    @Query("SELECT f FROM FileInfo f WHERE f.lastAccessTime < :cutoffTime")
    List<FileInfo> findUnusedFiles(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    @Query("SELECT SUM(f.fileSize) FROM FileInfo f")
    Long getTotalFileSize();
    
    @Query("SELECT COUNT(f) FROM FileInfo f WHERE f.uploadTime >= :startTime")
    Long countFilesUploadedSince(@Param("startTime") LocalDateTime startTime);
}