package com.example.photoupload.controller;

import com.example.photoupload.dto.ApiResponse;
import com.example.photoupload.dto.FileUploadResponse;
import com.example.photoupload.model.FileInfo;
import com.example.photoupload.service.FileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 文件管理控制器
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/files")
@Tag(name = "文件管理", description = "文件上传、下载和预览接口")
public class FileController {
    
    private static final Logger logger = LoggerFactory.getLogger(FileController.class);
    
    @Autowired
    private FileService fileService;
    
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "上传单个文件", description = "上传单个图片文件")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "上传成功"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "请求参数错误"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public ResponseEntity<ApiResponse<FileUploadResponse>> uploadFile(
            @Parameter(description = "要上传的文件", required = true)
            @RequestParam("file") MultipartFile file) {
        
        try {
            logger.info("开始上传文件: {}", file.getOriginalFilename());
            FileUploadResponse response = fileService.uploadSingleFile(file);
            logger.info("文件上传成功: {}", response.getFileName());
            return ResponseEntity.ok(ApiResponse.success("文件上传成功", response));
        } catch (Exception e) {
            logger.error("文件上传失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("文件上传失败: " + e.getMessage()));
        }
    }
    
    @PostMapping(value = "/upload/batch", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "批量上传文件", description = "批量上传多个图片文件")
    public ResponseEntity<ApiResponse<List<FileUploadResponse>>> uploadFiles(
            @Parameter(description = "要上传的文件列表", required = true)
            @RequestParam("files") List<MultipartFile> files) {
        
        try {
            logger.info("开始批量上传文件，数量: {}", files.size());
            List<FileUploadResponse> responses = fileService.uploadFiles(files);
            logger.info("批量上传成功，数量: {}", responses.size());
            return ResponseEntity.ok(ApiResponse.success("批量上传成功", responses));
        } catch (Exception e) {
            logger.error("批量上传失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("批量上传失败: " + e.getMessage()));
        }
    }
    
    @GetMapping("/download/{fileName}")
    @Operation(summary = "下载文件", description = "根据文件名下载文件")
    public ResponseEntity<Resource> downloadFile(
            @Parameter(description = "文件名", required = true)
            @PathVariable String fileName,
            @Parameter(description = "是否强制下载", required = false)
            @RequestParam(value = "attachment", defaultValue = "false") boolean attachment) {
        
        try {
            logger.info("开始下载文件: {}", fileName);
            
            FileInfo fileInfo = fileService.getFileInfo(fileName);
            byte[] fileContent = fileService.getFileContent(fileName);
            
            ByteArrayResource resource = new ByteArrayResource(fileContent);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(fileInfo.getContentType()));
            headers.setContentLength(fileContent.length);
            
            if (attachment) {
                String encodedFileName = URLEncoder.encode(fileInfo.getOriginalFileName(), 
                        StandardCharsets.UTF_8.toString());
                headers.setContentDispositionFormData("attachment", encodedFileName);
            }
            
            logger.info("文件下载成功: {}", fileName);
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);
                    
        } catch (Exception e) {
            logger.error("文件下载失败: {}", fileName, e);
            return ResponseEntity.notFound().build();
        }
    }
    
    @GetMapping("/preview/{fileName}")
    @Operation(summary = "预览文件", description = "在线预览图片文件")
    public ResponseEntity<Resource> previewFile(
            @Parameter(description = "文件名", required = true)
            @PathVariable String fileName) {
        
        try {
            logger.info("开始预览文件: {}", fileName);
            
            FileInfo fileInfo = fileService.getFileInfo(fileName);
            byte[] fileContent = fileService.getFileContent(fileName);
            
            ByteArrayResource resource = new ByteArrayResource(fileContent);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(fileInfo.getContentType()));
            headers.setContentLength(fileContent.length);
            headers.setCacheControl("max-age=3600");
            
            logger.info("文件预览成功: {}", fileName);
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);
                    
        } catch (Exception e) {
            logger.error("文件预览失败: {}", fileName, e);
            return ResponseEntity.notFound().build();
        }
    }
    
    @GetMapping("/thumbnail/{fileName}")
    @Operation(summary = "获取缩略图", description = "获取图片的缩略图")
    public ResponseEntity<Resource> getThumbnail(
            @Parameter(description = "文件名", required = true)
            @PathVariable String fileName) {
        
        try {
            logger.info("开始获取缩略图: {}", fileName);
            
            FileInfo fileInfo = fileService.getFileInfo(fileName);
            if (fileInfo.getThumbnailPath() == null) {
                return ResponseEntity.notFound().build();
            }
            
            byte[] thumbnailContent = fileService.getFileContent("thumb_" + fileName);
            ByteArrayResource resource = new ByteArrayResource(thumbnailContent);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(fileInfo.getContentType()));
            headers.setContentLength(thumbnailContent.length);
            headers.setCacheControl("max-age=86400");
            
            logger.info("缩略图获取成功: {}", fileName);
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);
                    
        } catch (Exception e) {
            logger.error("缩略图获取失败: {}", fileName, e);
            return ResponseEntity.notFound().build();
        }
    }
    
    @GetMapping("/list")
    @Operation(summary = "获取文件列表", description = "获取所有已上传的文件列表")
    public ResponseEntity<ApiResponse<List<FileInfo>>> listFiles() {
        try {
            List<FileInfo> files = fileService.getAllFiles();
            return ResponseEntity.ok(ApiResponse.success("获取文件列表成功", files));
        } catch (Exception e) {
            logger.error("获取文件列表失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取文件列表失败: " + e.getMessage()));
        }
    }
    
    @DeleteMapping("/{fileName}")
    @Operation(summary = "删除文件", description = "根据文件名删除文件")
    public ResponseEntity<ApiResponse<String>> deleteFile(
            @Parameter(description = "文件名", required = true)
            @PathVariable String fileName) {
        
        try {
            logger.info("开始删除文件: {}", fileName);
            boolean deleted = fileService.deleteFile(fileName);
            
            if (deleted) {
                logger.info("文件删除成功: {}", fileName);
                return ResponseEntity.ok(ApiResponse.success("文件删除成功"));
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("文件不存在: " + fileName));
            }
        } catch (Exception e) {
            logger.error("文件删除失败: {}", fileName, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("文件删除失败: " + e.getMessage()));
        }
    }
    
    @GetMapping("/info/{fileName}")
    @Operation(summary = "获取文件信息", description = "根据文件名获取文件详细信息")
    public ResponseEntity<ApiResponse<FileInfo>> getFileInfo(
            @Parameter(description = "文件名", required = true)
            @PathVariable String fileName) {
        
        try {
            FileInfo fileInfo = fileService.getFileInfo(fileName);
            return ResponseEntity.ok(ApiResponse.success("获取文件信息成功", fileInfo));
        } catch (Exception e) {
            logger.error("获取文件信息失败: {}", fileName, e);
            return ResponseEntity.notFound()
                    .build();
        }
    }
}