package com.example.photoupload.dto;

import java.time.LocalDateTime;

/**
 * 文件上传响应DTO
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class FileUploadResponse {
    
    private Long id;
    private String fileName;
    private String originalFileName;
    private String downloadUrl;
    private String previewUrl;
    private String thumbnailUrl;
    private String contentType;
    private Long fileSize;
    private LocalDateTime uploadTime;
    
    public FileUploadResponse() {
    }
    
    public FileUploadResponse(Long id, String fileName, String originalFileName, 
                             String downloadUrl, String previewUrl, String thumbnailUrl,
                             String contentType, Long fileSize, LocalDateTime uploadTime) {
        this.id = id;
        this.fileName = fileName;
        this.originalFileName = originalFileName;
        this.downloadUrl = downloadUrl;
        this.previewUrl = previewUrl;
        this.thumbnailUrl = thumbnailUrl;
        this.contentType = contentType;
        this.fileSize = fileSize;
        this.uploadTime = uploadTime;
    }
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    
    public String getOriginalFileName() {
        return originalFileName;
    }
    
    public void setOriginalFileName(String originalFileName) {
        this.originalFileName = originalFileName;
    }
    
    public String getDownloadUrl() {
        return downloadUrl;
    }
    
    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }
    
    public String getPreviewUrl() {
        return previewUrl;
    }
    
    public void setPreviewUrl(String previewUrl) {
        this.previewUrl = previewUrl;
    }
    
    public String getThumbnailUrl() {
        return thumbnailUrl;
    }
    
    public void setThumbnailUrl(String thumbnailUrl) {
        this.thumbnailUrl = thumbnailUrl;
    }
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public Long getFileSize() {
        return fileSize;
    }
    
    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }
    
    public LocalDateTime getUploadTime() {
        return uploadTime;
    }
    
    public void setUploadTime(LocalDateTime uploadTime) {
        this.uploadTime = uploadTime;
    }
}