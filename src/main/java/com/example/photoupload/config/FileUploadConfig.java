package com.example.photoupload.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.util.List;

/**
 * 文件上传配置类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Configuration
@ConfigurationProperties(prefix = "file.upload")
@Validated
public class FileUploadConfig {
    
    @NotNull
    private String dir;
    
    @Positive
    private long maxSize = 10485760; // 10MB
    
    @NotNull
    private List<String> allowedExtensions;
    
    private double imageQuality = 0.8;
    
    private int maxFilesPerRequest = 5;
    
    private ThumbnailConfig thumbnail = new ThumbnailConfig();
    
    public String getDir() {
        return dir;
    }
    
    public void setDir(String dir) {
        this.dir = dir;
    }
    
    public long getMaxSize() {
        return maxSize;
    }
    
    public void setMaxSize(long maxSize) {
        this.maxSize = maxSize;
    }
    
    public List<String> getAllowedExtensions() {
        return allowedExtensions;
    }
    
    public void setAllowedExtensions(List<String> allowedExtensions) {
        this.allowedExtensions = allowedExtensions;
    }
    
    public double getImageQuality() {
        return imageQuality;
    }
    
    public void setImageQuality(double imageQuality) {
        this.imageQuality = imageQuality;
    }
    
    public int getMaxFilesPerRequest() {
        return maxFilesPerRequest;
    }
    
    public void setMaxFilesPerRequest(int maxFilesPerRequest) {
        this.maxFilesPerRequest = maxFilesPerRequest;
    }
    
    public ThumbnailConfig getThumbnail() {
        return thumbnail;
    }
    
    public void setThumbnail(ThumbnailConfig thumbnail) {
        this.thumbnail = thumbnail;
    }
    
    public static class ThumbnailConfig {
        private int width = 200;
        private int height = 200;
        
        public int getWidth() {
            return width;
        }
        
        public void setWidth(int width) {
            this.width = width;
        }
        
        public int getHeight() {
            return height;
        }
        
        public void setHeight(int height) {
            this.height = height;
        }
    }
}