package com.example.photoupload.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 防盗链拦截器
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ReferrerCheckInterceptor implements HandlerInterceptor {
    
    private static final Logger logger = LoggerFactory.getLogger(ReferrerCheckInterceptor.class);
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String referer = request.getHeader("Referer");
        String userAgent = request.getHeader("User-Agent");
        String requestUri = request.getRequestURI();
        
        logger.debug("访问请求 - URI: {}, Referer: {}, UserAgent: {}", requestUri, referer, userAgent);
        
        if (userAgent != null && userAgent.toLowerCase().contains("bot")) {
            logger.warn("检测到爬虫访问: {}", userAgent);
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            return false;
        }
        
        if (referer != null && !isValidReferer(referer, request)) {
            logger.warn("检测到非法引用: {}", referer);
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            return false;
        }
        
        return true;
    }
    
    private boolean isValidReferer(String referer, HttpServletRequest request) {
        String serverName = request.getServerName();
        String serverPort = ":" + request.getServerPort();
        
        if (referer.contains(serverName)) {
            return true;
        }
        
        if (referer.contains("localhost") || referer.contains("127.0.0.1")) {
            return true;
        }
        
        return false;
    }
}