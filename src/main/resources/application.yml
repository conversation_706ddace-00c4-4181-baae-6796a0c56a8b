server:
  port: 8080
  servlet:
    context-path: /api
  tomcat:
    max-file-size: 10MB
    max-request-size: 10MB

spring:
  application:
    name: photo-upload-system
  
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 10MB
      file-size-threshold: 2KB
      location: /tmp
  
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: password
  
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: update
    show-sql: false
  
  h2:
    console:
      enabled: true
      path: /h2-console
  
  security:
    user:
      name: admin
      password: admin123
      roles: ADMIN
  
  cache:
    type: simple

file:
  upload:
    dir: ${user.home}/uploads
    max-size: 10485760  # 10MB in bytes
    allowed-extensions: jpg,jpeg,png,gif,bmp,webp
    image-quality: 0.8
    thumbnail:
      width: 200
      height: 200
    max-files-per-request: 5

logging:
  level:
    com.example: DEBUG
    org.springframework.web.multipart: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/photo-upload-system.log

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always